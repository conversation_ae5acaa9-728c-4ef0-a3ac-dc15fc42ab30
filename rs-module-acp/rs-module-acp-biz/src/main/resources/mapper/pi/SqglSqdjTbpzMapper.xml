<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.pi.SqglSqdjTbpzDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    
    <select id="getSyncList" resultType="com.alibaba.fastjson.JSONObject">
        ${sql}
    </select>
    
    <select id="getAreaByGbcode" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            t1.area_id,
            t2.area_name
        FROM
            acp_pm_device t1
                LEFT JOIN acp_pm_area t2 on t1.area_id = t2.id
        WHERE
            t1.gb_code = #{gbcode}
          and t1.is_del = 0
    </select>

</mapper>
