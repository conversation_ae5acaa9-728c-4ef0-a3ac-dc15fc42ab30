package com.rs.module.tem.service.talk;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.util.SessionUserUtil;
import com.fhs.trans.service.impl.TransService;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.BspDbUtil;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.enums.AreaTypeEnum;
import com.rs.module.base.service.pm.AreaService;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.service.pm.device.DeviceAuthService;
import com.rs.module.pdf.enums.PdfTemplateEnum;
import com.rs.module.tem.controller.admin.talk.vo.TalkRecordListReqVO;
import com.rs.module.tem.controller.admin.talk.vo.TalkRecordPageReqVO;
import com.rs.module.tem.controller.admin.talk.vo.TalkRecordRespVO;
import com.rs.module.tem.controller.admin.talk.vo.TalkRecordSaveReqVO;
import com.rs.module.tem.controller.app.record.GetRecordListByJgrybmVO;
import com.rs.module.tem.dao.talk.TalkRecordDao;
import com.rs.module.tem.dao.talk.TalkTaskDao;
import com.rs.module.tem.entity.talk.TalkRecordDO;
import com.rs.module.tem.entity.talk.TalkTaskDO;
import com.rs.util.PDFUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 谈话教育-个人谈话教育 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Log4j2
public class TalkRecordServiceImpl extends BaseServiceImpl<TalkRecordDao, TalkRecordDO> implements TalkRecordService {

    @Resource
    private TalkRecordDao talkRecordDao;

    @Resource
    private TalkTaskDao talkTaskDao;
    @Resource
    private DeviceAuthService devAuthService;

    @Resource
    private AreaService areaService;

    @Resource
    private TransService transService;
    @Resource
    private PrisonerService prisonerService;

    @Override
    public String createTalkRecord(TalkRecordSaveReqVO createReqVO) {
        // 插入
        TalkRecordDO talkRecord = BeanUtils.toBean(createReqVO, TalkRecordDO.class);
        TalkTaskDO talkTaskDO = talkTaskDao.selectOne(TalkTaskDO::getTalkCode, talkRecord.getTalkCode());

        //更新谈话记录表的jgrybm
        talkRecord.setJgrybm(talkTaskDO.getJgrybm());

        //谈话原因
        talkTaskDO.setTalkReason(createReqVO.getTalkReason());
        talkTaskDao.updateById(talkTaskDO);

        //谈话待办
        talkRecord.setTaskSource(talkTaskDO.getTaskSource());
        talkRecord.setVideoUrl(talkTaskDO.getVideoUrl());

        talkRecordDao.insert(talkRecord);
        // 返回
        return talkRecord.getId();
    }

    @Override
    public void updateTalkRecord(TalkRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateTalkRecordExists(updateReqVO.getId());
        // 更新
        TalkRecordDO updateObj = BeanUtils.toBean(updateReqVO, TalkRecordDO.class);
        talkRecordDao.updateById(updateObj);
    }

    @Override
    public void deleteTalkRecord(String id) {
        // 校验存在
        validateTalkRecordExists(id);
        // 删除
        talkRecordDao.deleteById(id);
    }

    private void validateTalkRecordExists(String id) {
        if (talkRecordDao.selectById(id) == null) {
            throw new ServerException("谈话教育-个人谈话教育数据不存在");
        }
    }

    @Override
    public TalkRecordDO getTalkRecord(String id) {
        return talkRecordDao.selectById(id);
    }

    @Override
    public PageResult<TalkRecordDO> getTalkRecordPage(TalkRecordPageReqVO pageReqVO) {
        return talkRecordDao.selectPage(pageReqVO);
    }

    @Override
    public List<TalkRecordDO> getTalkRecordList(TalkRecordListReqVO listReqVO) {
        return talkRecordDao.selectList(listReqVO);
    }

    @Override
    public List<AreaDO> getThs(HttpServletRequest request) {
        return areaService.lambdaQuery()
                .eq(AreaDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode())
                .eq(AreaDO::getAreaType, AreaTypeEnum.TALKING_ROOM.getCode())
                .list();
    }

    @Override
    public List<TalkRecordDO> getPersonTaskRecordEndListByJgrybm(String jgrybm, String talkReason) {
        return talkRecordDao.selectList(new LambdaQueryWrapper<TalkRecordDO>()
                .like(StrUtil.isNotBlank(talkReason), TalkRecordDO::getTalkReason, talkReason)
                .eq(TalkRecordDO::getJgrybm, jgrybm).isNotNull(TalkRecordDO::getEndTime).orderByDesc(TalkRecordDO::getEndTime));
    }


    @Override
    public List<TalkRecordDO> getRecordListByJgrybm(GetRecordListByJgrybmVO getRecordListByJgrybmVO) {
        LambdaQueryWrapper<TalkRecordDO> wrapper = new LambdaQueryWrapper<TalkRecordDO>()
                .eq(TalkRecordDO::getJgrybm, getRecordListByJgrybmVO.getJgrybm());

        // 根据rangType设置日期过滤条件
        String rangType = getRecordListByJgrybmVO.getRangType();
        if (StrUtil.isNotBlank(rangType)) {
            Date[] dateRange = getRecordListByJgrybmVO.calculateDateRange();
            if (dateRange != null && dateRange.length == 2) {
                wrapper.ge(TalkRecordDO::getAddTime, dateRange[0])
                        .le(TalkRecordDO::getAddTime, dateRange[1]);
            }
        }
        return talkRecordDao.selectList(wrapper.orderByDesc(TalkRecordDO::getAddTime));
    }

    @Override
    public byte[] getPdf(String id) {
        TalkRecordDO talkRecordDO = talkRecordDao.selectById(id);
        TalkRecordRespVO talkRecordRespVO = BeanUtils.toBean(talkRecordDO, TalkRecordRespVO.class);
        String talkContent = talkRecordRespVO.getTalkContent();
        JSONArray jsonArray = new JSONArray();
        if (StringUtils.isNotEmpty(talkContent)) {
            jsonArray = JSONArray.parseArray(talkContent);
        }
        PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm(talkRecordRespVO.getJgrybm());
        talkRecordRespVO.setPrisoner(prisoner);
        transService.transOne(talkRecordRespVO);
        Map<String, Object> map = BeanUtil.beanToMap(talkRecordRespVO);
        map.put("data", jsonArray);

        return PDFUtil.getPdf(PdfTemplateEnum.THBL, map);

    }

    @Override
    public void stopTalk(String id) {
        log.info("开始停止谈话，任务ID: {}", id);

        new Thread(() -> {
            try {
                log.info("异步线程开始处理停止谈话请求，任务ID: {}", id);

                // 查询谈话任务信息
                TalkTaskDO talkTaskDO = talkTaskDao.selectById(id);
                if (talkTaskDO == null) {
                    log.error("未找到谈话任务，任务ID: {}", id);
                    return;
                }
                log.info("查询到谈话任务信息，快鱼ID: {}, 监管人员编码: {}", talkTaskDO.getKyId(), talkTaskDO.getJgrybm());

                // 获取快鱼谈话URL配置
                String kyTalkUrl = BspDbUtil.getParam("KY_TALK_URL");
                if (StrUtil.isBlank(kyTalkUrl)) {
                    log.error("快鱼谈话URL配置为空，无法停止谈话，任务ID: {}", id);
                    return;
                }
                log.info("获取快鱼谈话URL配置: {}", kyTalkUrl);

                // 构建停止谈话请饭求
                String stopUrl = kyTalkUrl + "/api/record/stop/" + talkTaskDO.getKyId();
                log.info("构建停止谈话请求URL: {}", stopUrl);

                HttpRequest request = HttpRequest.put(stopUrl);
                log.info("发送停止谈话请求，快鱼ID: {}", talkTaskDO.getKyId());

                String body = request.execute().body();
                log.info("停止谈话请求响应: {}", body);

                log.info("成功完成停止谈话操作，任务ID: {}", id);

            } catch (Exception e) {
                log.error("停止谈话过程中发生异常，任务ID: {}", id, e);
            }
        }).start();

    }

    @Override
    public void startTalk(String id) {
        log.info("开始启动谈话，任务ID: {}", id);

        try {
            // 查询谈话任务信息
            TalkTaskDO talkTaskDO = talkTaskDao.selectById(id);
            if (talkTaskDO == null) {
                log.error("未找到谈话任务，任务ID: {}", id);
                throw new ServerException("未找到谈话任务");
            }
            log.info("查询到谈话任务信息，监管人员编码: {}", talkTaskDO.getJgrybm());

            // 获取配置参数
            String kyTalkUrl = BspDbUtil.getParam("KY_TALK_URL");
            String tempId = BspDbUtil.getParam("KY_TALK_TEMP_ID");
            log.info("获取配置参数 - 快鱼谈话URL: {}, 模板ID: {}", kyTalkUrl, tempId);

            if (StrUtil.isBlank(kyTalkUrl) || StrUtil.isBlank(tempId)) {
                log.error("配置参数缺失 - kyTalkUrl: {}, tempId: {}", kyTalkUrl, tempId);
                throw new ServerException("系统配置参数缺失");
            }

            // 构建请求参数
            Map<String, Object> param = new HashMap<>();
            PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm(talkTaskDO.getJgrybm());
            if (prisoner == null) {
                log.error("未找到监管人员信息，监管人员编码: {}", talkTaskDO.getJgrybm());
                throw new ServerException("未找到监管人员信息");
            }
            log.info("查询到监管人员信息 - 区域ID: {}, 监舍号: {}", prisoner.getAreaId(), prisoner.getJsh());

            param.put("talkerCode", "0123456");
            param.put("regionCode","d402");
            param.put("locutoryId", "18");
            param.put("intervieweeCode", talkTaskDO.getJgrybm());
            param.put("templateId", tempId);

            log.info("构建请求参数完成 - intervieweeCode: {}, templateId: {}", talkTaskDO.getJgrybm(), tempId);

            // 异步调用第三方接口
            new Thread(() -> {
                try {
                    log.info("开始调用第三方谈话接口，URL: {}, 参数: {}", kyTalkUrl + "/api/talk/third", JSON.toJSONString(param));

                    String post = HttpUtil.post(kyTalkUrl + "/api/talk/third", JSON.toJSONString(param));
                    log.info("第三方谈话接口响应: {}", post);

                    JSONObject res = JSONObject.parseObject(post);
                    Integer code = res.getInteger("code");
                    log.info("解析响应结果 - code: {}", code);

                    if (code == 0) {
                        String kyId = res.getString("data");
                        log.info("谈话创建成功，获得快鱼ID: {}", kyId);

                        // 更新任务的快鱼ID
                        talkTaskDO.setKyId(kyId);
                        int updateResult = talkTaskDao.updateById(talkTaskDO);
                        log.info("更新任务快鱼ID结果: {}", updateResult > 0 ? "成功" : "失败");


                        String startResp = HttpRequest.post(kyTalkUrl + "/api/record/start?id=" + res.getString("data")).execute().body();
                        log.info("开始响应: {}", startResp);

                        JSONObject startRes = JSONObject.parseObject(startResp);
                        if (startRes != null && startRes.getInteger("code") == 0) {
                            log.info("开始响应成功");
                        } else {
                            log.error("开始响应失败，响应: {}", startResp);
                        }
                    } else {
                        String message = res.getString("message");
                        log.error("第三方谈话接口调用失败 - code: {}, message: {}", code, message);
                    }
                } catch (Exception e) {
                    log.error("异步调用第三方接口异常，任务ID: {}", id, e);
                }
            }).start();

            log.info("谈话启动流程完成，任务ID: {}", id);

        } catch (Exception e) {
            log.error("启动谈话异常，任务ID: {}", id, e);
            throw e;
        }
    }

}
